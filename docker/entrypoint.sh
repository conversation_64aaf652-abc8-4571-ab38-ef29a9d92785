#!/bin/bash

# Laravel Queue Worker Docker Entrypoint Script
# Bu script Docker container başlatıldığında çalışır ve queue worker'ları hazırlar

set -e

echo "🚀 Laravel Queue Worker Container başlatılıyor..."

# Renk kodları
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Log fonksiyonu
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Gerekli dizinleri oluştur
log "Gerekli dizinler oluşturuluyor..."
mkdir -p /var/www/storage/logs
mkdir -p /var/www/storage/framework/cache
mkdir -p /var/www/storage/framework/sessions
mkdir -p /var/www/storage/framework/views
mkdir -p /var/www/bootstrap/cache
mkdir -p /var/log/supervisor

# İzinleri ayarla
log "Dosya izinleri ayarlanıyor..."
chmod -R 755 /var/www/storage
chmod -R 755 /var/www/bootstrap/cache
chown -R www-data:www-data /var/www/storage
chown -R www-data:www-data /var/www/bootstrap/cache

# Laravel hazırlıkları
log "Laravel hazırlıkları yapılıyor..."

# Cache temizleme ve optimizasyon
if [ "$APP_ENV" = "production" ]; then
    log "Production ortamı için optimizasyonlar yapılıyor..."
    php artisan config:cache
    php artisan route:cache
    php artisan view:cache
    php artisan event:cache
else
    log "Development ortamı için cache temizleniyor..."
    php artisan config:clear
    php artisan route:clear
    php artisan view:clear
    php artisan cache:clear
fi

# Database bağlantısını kontrol et
log "Database bağlantısı kontrol ediliyor..."
max_attempts=30
attempt=1

while [ $attempt -le $max_attempts ]; do
    if php artisan migrate:status > /dev/null 2>&1; then
        log "Database bağlantısı başarılı!"
        break
    else
        warn "Database bağlantısı kurulamadı. Deneme $attempt/$max_attempts"
        sleep 2
        attempt=$((attempt + 1))
    fi
done

if [ $attempt -gt $max_attempts ]; then
    error "Database bağlantısı kurulamadı. Container durduruluyor."
    exit 1
fi

# Migration'ları çalıştır (sadece production'da otomatik)
if [ "$AUTO_MIGRATE" = "true" ] || [ "$APP_ENV" = "production" ]; then
    log "Database migration'ları çalıştırılıyor..."
    php artisan migrate --force
fi

# Redis bağlantısını kontrol et
log "Redis bağlantısı kontrol ediliyor..."
max_attempts=15
attempt=1

while [ $attempt -le $max_attempts ]; do
    if php artisan tinker --execute="Redis::ping();" > /dev/null 2>&1; then
        log "Redis bağlantısı başarılı!"
        break
    else
        warn "Redis bağlantısı kurulamadı. Deneme $attempt/$max_attempts"
        sleep 2
        attempt=$((attempt + 1))
    fi
done

if [ $attempt -gt $max_attempts ]; then
    error "Redis bağlantısı kurulamadı. Queue worker'lar çalışmayabilir."
fi

# Queue worker stratejisini belirle
QUEUE_STRATEGY=${QUEUE_STRATEGY:-"horizon"}

log "Queue worker stratejisi: $QUEUE_STRATEGY"

case $QUEUE_STRATEGY in
    "horizon")
        log "Horizon kullanılacak - diğer queue worker'lar devre dışı"
        # Horizon aktif, diğer worker'lar pasif
        ;;
    "workers")
        log "Geleneksel queue worker'lar kullanılacak - Horizon devre dışı"
        # Horizon'u devre dışı bırak, worker'ları aktif et
        sed -i 's/autostart=true/autostart=false/' /etc/supervisor/conf.d/supervisord.conf
        sed -i '/\[program:queue-/,/priority=[0-9]*/ s/autostart=false/autostart=true/' /etc/supervisor/conf.d/supervisord.conf
        ;;
    "both")
        log "Hem Horizon hem de worker'lar kullanılacak (dikkatli kullanın!)"
        # Hem Horizon hem worker'lar aktif
        sed -i '/\[program:queue-/,/priority=[0-9]*/ s/autostart=false/autostart=true/' /etc/supervisor/conf.d/supervisord.conf
        ;;
    *)
        warn "Bilinmeyen queue stratejisi: $QUEUE_STRATEGY. Horizon kullanılacak."
        ;;
esac

# Queue tablolarını kontrol et ve oluştur
log "Queue tabloları kontrol ediliyor..."
php artisan queue:table > /dev/null 2>&1 || true
php artisan queue:failed-table > /dev/null 2>&1 || true

# Horizon için gerekli tablolar
if [ "$QUEUE_STRATEGY" = "horizon" ] || [ "$QUEUE_STRATEGY" = "both" ]; then
    log "Horizon tabloları kontrol ediliyor..."
    php artisan horizon:install > /dev/null 2>&1 || true
fi

# Failed job'ları temizle (opsiyonel)
if [ "$CLEAR_FAILED_JOBS" = "true" ]; then
    log "Başarısız job'lar temizleniyor..."
    php artisan queue:flush
fi

# Supervisor log dosyalarını hazırla
log "Supervisor log dosyaları hazırlanıyor..."
touch /var/log/supervisor/supervisord.log
touch /var/www/storage/logs/horizon.log
touch /var/www/storage/logs/horizon-error.log
touch /var/www/storage/logs/queue-default.log
touch /var/www/storage/logs/queue-emails.log
touch /var/www/storage/logs/queue-orders.log
touch /var/www/storage/logs/queue-realtime.log
touch /var/www/storage/logs/queue-realtime-critical.log
touch /var/www/storage/logs/queue-inventory.log
touch /var/www/storage/logs/queue-shipping.log
touch /var/www/storage/logs/scheduler.log

# Graceful shutdown handler
cleanup() {
    log "Container kapatılıyor, worker'lar güvenli şekilde durduruluyor..."
    
    if [ "$QUEUE_STRATEGY" = "horizon" ] || [ "$QUEUE_STRATEGY" = "both" ]; then
        log "Horizon durduruluyor..."
        php artisan horizon:terminate
        sleep 5
    fi
    
    log "Supervisor durduruluyor..."
    supervisorctl shutdown
    
    log "Container güvenli şekilde kapatıldı."
    exit 0
}

# Signal handler'ları ayarla
trap cleanup SIGTERM SIGINT

# Sistem bilgilerini logla
log "=== Sistem Bilgileri ==="
info "PHP Version: $(php -v | head -n 1)"
info "Laravel Version: $(php artisan --version)"
info "Queue Strategy: $QUEUE_STRATEGY"
info "Environment: $APP_ENV"
info "Debug Mode: $APP_DEBUG"
info "Queue Connection: $(php artisan tinker --execute='echo config("queue.default");')"
log "========================"

# Son kontroller
log "Son kontroller yapılıyor..."

# Artisan komutunun çalıştığını kontrol et
if ! php artisan list > /dev/null 2>&1; then
    error "Laravel artisan çalışmıyor!"
    exit 1
fi

log "✅ Tüm hazırlıklar tamamlandı!"
log "🎯 Supervisor başlatılıyor..."

# Supervisor'ı başlat
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf

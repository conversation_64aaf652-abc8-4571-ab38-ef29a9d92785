#!/bin/bash

# <PERSON><PERSON> Queue Worker Manager Script
# Bu script queue worker'ları yönetmek için kullanılır

set -e

# Renk kodları
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Log fonksiyonları
log() { echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"; }
warn() { echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"; }
error() { echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"; }
info() { echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"; }

# Kullanım bilgisi
usage() {
    echo "Laravel Queue Worker Manager"
    echo ""
    echo "Kullanım: $0 [KOMUT] [SEÇENEKLER]"
    echo ""
    echo "Komutlar:"
    echo "  status              - Tüm worker'ların durumunu göster"
    echo "  start [worker]      - Worker'ı başlat (tümü için boş bırak)"
    echo "  stop [worker]       - Worker'ı durdur (tümü için boş bırak)"
    echo "  restart [worker]    - Worker'ı yeniden başlat (tümü için boş bırak)"
    echo "  logs [worker]       - Worker loglarını göster"
    echo "  monitor             - Canlı monitoring"
    echo "  health              - Sistem sağlık kontrolü"
    echo "  switch [strategy]   - Queue stratejisini değiştir (horizon/workers/both)"
    echo ""
    echo "Worker'lar:"
    echo "  horizon             - Laravel Horizon"
    echo "  queue-default       - Default queue worker"
    echo "  queue-emails        - Email queue worker"
    echo "  queue-orders        - Orders queue worker"
    echo "  queue-realtime      - Realtime queue worker"
    echo "  queue-realtime-critical - Critical realtime queue worker"
    echo "  queue-inventory     - Inventory queue worker"
    echo "  queue-shipping      - Shipping queue worker"
    echo "  scheduler           - Laravel scheduler"
    echo ""
    echo "Örnekler:"
    echo "  $0 status"
    echo "  $0 start queue-emails"
    echo "  $0 restart horizon"
    echo "  $0 logs queue-orders"
    echo "  $0 switch workers"
}

# Worker listesi
WORKERS=("horizon" "queue-default" "queue-emails" "queue-orders" "queue-realtime" "queue-realtime-critical" "queue-inventory" "queue-shipping" "scheduler")

# Supervisor komutları
supervisor_status() {
    supervisorctl status 2>/dev/null || echo "Supervisor çalışmıyor"
}

supervisor_start() {
    local worker=$1
    if [ -z "$worker" ]; then
        supervisorctl start all
    else
        supervisorctl start "$worker:*" 2>/dev/null || supervisorctl start "$worker" 2>/dev/null
    fi
}

supervisor_stop() {
    local worker=$1
    if [ -z "$worker" ]; then
        supervisorctl stop all
    else
        supervisorctl stop "$worker:*" 2>/dev/null || supervisorctl stop "$worker" 2>/dev/null
    fi
}

supervisor_restart() {
    local worker=$1
    if [ -z "$worker" ]; then
        supervisorctl restart all
    else
        supervisorctl restart "$worker:*" 2>/dev/null || supervisorctl restart "$worker" 2>/dev/null
    fi
}

# Status komutu
cmd_status() {
    log "Queue Worker Durumu:"
    echo ""
    supervisor_status | while read line; do
        if echo "$line" | grep -q "RUNNING"; then
            echo -e "${GREEN}✓${NC} $line"
        elif echo "$line" | grep -q "STOPPED"; then
            echo -e "${RED}✗${NC} $line"
        elif echo "$line" | grep -q "FATAL"; then
            echo -e "${RED}💀${NC} $line"
        else
            echo -e "${YELLOW}?${NC} $line"
        fi
    done
    echo ""
    
    # Queue istatistikleri
    if command -v php >/dev/null 2>&1; then
        log "Queue İstatistikleri:"
        php artisan queue:monitor 2>/dev/null || warn "Queue monitoring bilgisi alınamadı"
    fi
}

# Start komutu
cmd_start() {
    local worker=$1
    if [ -z "$worker" ]; then
        log "Tüm worker'lar başlatılıyor..."
        supervisor_start
    else
        log "Worker başlatılıyor: $worker"
        supervisor_start "$worker"
    fi
    sleep 2
    cmd_status
}

# Stop komutu
cmd_stop() {
    local worker=$1
    if [ -z "$worker" ]; then
        log "Tüm worker'lar durduruluyor..."
        supervisor_stop
    else
        log "Worker durduruluyor: $worker"
        supervisor_stop "$worker"
    fi
    sleep 2
    cmd_status
}

# Restart komutu
cmd_restart() {
    local worker=$1
    if [ -z "$worker" ]; then
        log "Tüm worker'lar yeniden başlatılıyor..."
        supervisor_restart
    else
        log "Worker yeniden başlatılıyor: $worker"
        supervisor_restart "$worker"
    fi
    sleep 2
    cmd_status
}

# Logs komutu
cmd_logs() {
    local worker=$1
    if [ -z "$worker" ]; then
        log "Tüm loglar:"
        tail -f /var/www/storage/logs/*.log
    else
        case $worker in
            "horizon")
                tail -f /var/www/storage/logs/horizon.log /var/www/storage/logs/horizon-error.log
                ;;
            "scheduler")
                tail -f /var/www/storage/logs/scheduler.log /var/www/storage/logs/scheduler-error.log
                ;;
            *)
                if [[ " ${WORKERS[@]} " =~ " ${worker} " ]]; then
                    tail -f "/var/www/storage/logs/${worker}.log" "/var/www/storage/logs/${worker}-error.log" 2>/dev/null
                else
                    error "Bilinmeyen worker: $worker"
                    exit 1
                fi
                ;;
        esac
    fi
}

# Monitor komutu
cmd_monitor() {
    log "Canlı monitoring başlatılıyor... (Çıkmak için Ctrl+C)"
    while true; do
        clear
        echo "=== Laravel Queue Worker Monitor ==="
        echo "Son güncelleme: $(date)"
        echo ""
        cmd_status
        echo ""
        echo "=== Son Log Girişleri ==="
        tail -n 5 /var/www/storage/logs/*.log 2>/dev/null | head -20
        sleep 5
    done
}

# Health komutu
cmd_health() {
    log "Sistem sağlık kontrolü yapılıyor..."
    
    local health_score=0
    local total_checks=0
    
    # Supervisor kontrolü
    total_checks=$((total_checks + 1))
    if supervisorctl status >/dev/null 2>&1; then
        info "✓ Supervisor çalışıyor"
        health_score=$((health_score + 1))
    else
        error "✗ Supervisor çalışmıyor"
    fi
    
    # PHP kontrolü
    total_checks=$((total_checks + 1))
    if php -v >/dev/null 2>&1; then
        info "✓ PHP çalışıyor"
        health_score=$((health_score + 1))
    else
        error "✗ PHP çalışmıyor"
    fi
    
    # Laravel kontrolü
    total_checks=$((total_checks + 1))
    if php artisan --version >/dev/null 2>&1; then
        info "✓ Laravel erişilebilir"
        health_score=$((health_score + 1))
    else
        error "✗ Laravel erişilebilir değil"
    fi
    
    # Database kontrolü
    total_checks=$((total_checks + 1))
    if php artisan migrate:status >/dev/null 2>&1; then
        info "✓ Database bağlantısı OK"
        health_score=$((health_score + 1))
    else
        error "✗ Database bağlantısı problemi"
    fi
    
    # Redis kontrolü
    total_checks=$((total_checks + 1))
    if php artisan tinker --execute="Redis::ping();" >/dev/null 2>&1; then
        info "✓ Redis bağlantısı OK"
        health_score=$((health_score + 1))
    else
        error "✗ Redis bağlantısı problemi"
    fi
    
    # Disk alanı kontrolü
    total_checks=$((total_checks + 1))
    local disk_usage=$(df /var/www | tail -1 | awk '{print $5}' | sed 's/%//')
    if [ "$disk_usage" -lt 90 ]; then
        info "✓ Disk alanı yeterli (%$disk_usage)"
        health_score=$((health_score + 1))
    else
        warn "⚠ Disk alanı az (%$disk_usage)"
    fi
    
    # Sonuç
    local health_percentage=$((health_score * 100 / total_checks))
    echo ""
    if [ $health_percentage -ge 80 ]; then
        log "🎉 Sistem sağlığı: %$health_percentage (İyi)"
    elif [ $health_percentage -ge 60 ]; then
        warn "⚠️ Sistem sağlığı: %$health_percentage (Orta)"
    else
        error "🚨 Sistem sağlığı: %$health_percentage (Kötü)"
    fi
}

# Switch komutu
cmd_switch() {
    local strategy=$1
    if [ -z "$strategy" ]; then
        error "Strateji belirtilmeli: horizon, workers, both"
        exit 1
    fi
    
    case $strategy in
        "horizon")
            log "Horizon stratejisine geçiliyor..."
            supervisor_stop
            # Horizon aktif, diğerleri pasif
            sed -i '/\[program:horizon\]/,/priority=[0-9]*/ s/autostart=false/autostart=true/' /etc/supervisor/conf.d/supervisord.conf
            sed -i '/\[program:queue-/,/priority=[0-9]*/ s/autostart=true/autostart=false/' /etc/supervisor/conf.d/supervisord.conf
            supervisorctl reread
            supervisorctl update
            supervisor_start "horizon"
            ;;
        "workers")
            log "Worker stratejisine geçiliyor..."
            supervisor_stop
            # Worker'lar aktif, Horizon pasif
            sed -i '/\[program:horizon\]/,/priority=[0-9]*/ s/autostart=true/autostart=false/' /etc/supervisor/conf.d/supervisord.conf
            sed -i '/\[program:queue-/,/priority=[0-9]*/ s/autostart=false/autostart=true/' /etc/supervisor/conf.d/supervisord.conf
            supervisorctl reread
            supervisorctl update
            for worker in "${WORKERS[@]}"; do
                if [[ $worker == queue-* ]]; then
                    supervisor_start "$worker"
                fi
            done
            ;;
        "both")
            warn "Hem Horizon hem worker'lar aktif edilecek. Bu önerilmez!"
            read -p "Devam etmek istiyor musunuz? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                log "Her iki strateji de aktif ediliyor..."
                sed -i '/\[program:horizon\]/,/priority=[0-9]*/ s/autostart=false/autostart=true/' /etc/supervisor/conf.d/supervisord.conf
                sed -i '/\[program:queue-/,/priority=[0-9]*/ s/autostart=false/autostart=true/' /etc/supervisor/conf.d/supervisord.conf
                supervisorctl reread
                supervisorctl update
                supervisor_start
            else
                log "İşlem iptal edildi."
                exit 0
            fi
            ;;
        *)
            error "Bilinmeyen strateji: $strategy"
            exit 1
            ;;
    esac
    
    sleep 3
    cmd_status
}

# Ana komut işleyici
case "${1:-}" in
    "status")
        cmd_status
        ;;
    "start")
        cmd_start "$2"
        ;;
    "stop")
        cmd_stop "$2"
        ;;
    "restart")
        cmd_restart "$2"
        ;;
    "logs")
        cmd_logs "$2"
        ;;
    "monitor")
        cmd_monitor
        ;;
    "health")
        cmd_health
        ;;
    "switch")
        cmd_switch "$2"
        ;;
    "help"|"-h"|"--help")
        usage
        ;;
    "")
        usage
        ;;
    *)
        error "Bilinmeyen komut: $1"
        echo ""
        usage
        exit 1
        ;;
esac

[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid
loglevel=info
childlogdir=/var/log/supervisor

[unix_http_server]
file=/var/run/supervisor.sock
chmod=0700

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[program:php-fpm]
command=php-fpm
autostart=true
autorestart=true
user=root
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
priority=100

[program:horizon]
process_name=%(program_name)s
command=php /var/www/artisan horizon
autostart=true
autorestart=true
user=root
redirect_stderr=true
stdout_logfile=/var/www/storage/logs/horizon.log
stderr_logfile=/var/www/storage/logs/horizon-error.log
stopwaitsecs=3600
stopsignal=TERM
killasgroup=true
stopasgroup=true
priority=200

; Default Queue Worker (Fallback)
[program:queue-default]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/artisan queue:work redis --queue=default --sleep=3 --tries=3 --max-time=3600 --timeout=60
autostart=false
autorestart=true
user=root
numprocs=2
redirect_stderr=true
stdout_logfile=/var/www/storage/logs/queue-default.log
stderr_logfile=/var/www/storage/logs/queue-default-error.log
stopwaitsecs=3600
stopsignal=TERM
killasgroup=true
stopasgroup=true
priority=300

; Email Queue Worker
[program:queue-emails]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/artisan queue:work redis --queue=emails --sleep=3 --tries=3 --max-time=3600 --timeout=120
autostart=false
autorestart=true
user=root
numprocs=2
redirect_stderr=true
stdout_logfile=/var/www/storage/logs/queue-emails.log
stderr_logfile=/var/www/storage/logs/queue-emails-error.log
stopwaitsecs=3600
stopsignal=TERM
killasgroup=true
stopasgroup=true
priority=301

; Orders Queue Worker
[program:queue-orders]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/artisan queue:work redis --queue=orders --sleep=3 --tries=2 --max-time=3600 --timeout=300
autostart=false
autorestart=true
user=root
numprocs=3
redirect_stderr=true
stdout_logfile=/var/www/storage/logs/queue-orders.log
stderr_logfile=/var/www/storage/logs/queue-orders-error.log
stopwaitsecs=3600
stopsignal=TERM
killasgroup=true
stopasgroup=true
priority=302

; Realtime Queue Worker
[program:queue-realtime]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/artisan queue:work redis --queue=realtime --sleep=1 --tries=3 --max-time=1800 --timeout=30
autostart=false
autorestart=true
user=root
numprocs=2
redirect_stderr=true
stdout_logfile=/var/www/storage/logs/queue-realtime.log
stderr_logfile=/var/www/storage/logs/queue-realtime-error.log
stopwaitsecs=60
stopsignal=TERM
killasgroup=true
stopasgroup=true
priority=303

; Critical Realtime Queue Worker
[program:queue-realtime-critical]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/artisan queue:work redis --queue=realtime-critical --sleep=1 --tries=5 --max-time=1800 --timeout=15
autostart=false
autorestart=true
user=root
numprocs=2
redirect_stderr=true
stdout_logfile=/var/www/storage/logs/queue-realtime-critical.log
stderr_logfile=/var/www/storage/logs/queue-realtime-critical-error.log
stopwaitsecs=30
stopsignal=TERM
killasgroup=true
stopasgroup=true
priority=304

; Inventory Queue Worker
[program:queue-inventory]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/artisan queue:work redis --queue=inventory --sleep=3 --tries=3 --max-time=3600 --timeout=180
autostart=false
autorestart=true
user=root
numprocs=2
redirect_stderr=true
stdout_logfile=/var/www/storage/logs/queue-inventory.log
stderr_logfile=/var/www/storage/logs/queue-inventory-error.log
stopwaitsecs=3600
stopsignal=TERM
killasgroup=true
stopasgroup=true
priority=305

; Shipping Queue Worker
[program:queue-shipping]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/artisan queue:work redis --queue=shipping --sleep=3 --tries=3 --max-time=3600 --timeout=240
autostart=false
autorestart=true
user=root
numprocs=2
redirect_stderr=true
stdout_logfile=/var/www/storage/logs/queue-shipping.log
stderr_logfile=/var/www/storage/logs/queue-shipping-error.log
stopwaitsecs=3600
stopsignal=TERM
killasgroup=true
stopasgroup=true
priority=306

; Laravel Scheduler (Cron Jobs)
[program:scheduler]
process_name=%(program_name)s
command=php /var/www/artisan schedule:work
autostart=true
autorestart=true
user=root
redirect_stderr=true
stdout_logfile=/var/www/storage/logs/scheduler.log
stderr_logfile=/var/www/storage/logs/scheduler-error.log
stopwaitsecs=60
stopsignal=TERM
priority=400

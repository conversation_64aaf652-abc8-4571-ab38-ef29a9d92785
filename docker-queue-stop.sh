#!/bin/bash

# Lara<PERSON> Queue Worker Docker Durdurma Script'i
# Bu script Docker container'larını güvenli şekilde durdurur

set -e

# Renk kodları
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Log fonksiyonları
log() { echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"; }
warn() { echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"; }
error() { echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"; }
info() { echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"; }

# Banner
echo "
╔══════════════════════════════════════════════════════════════╗
║                Laravel Queue Worker Docker                   ║
║                    Durdurma Script'i                         ║
╚══════════════════════════════════════════════════════════════╝
"

# Kullanım bilgisi
usage() {
    echo "Kullanım: $0 [SEÇENEKLER]"
    echo ""
    echo "Seçenekler:"
    echo "  --graceful                          Queue'ları güvenli şekilde durdur"
    echo "  --force                             Hemen durdur (graceful shutdown yok)"
    echo "  --remove-volumes                    Volume'ları da sil"
    echo "  --remove-images                     Image'ları da sil"
    echo "  --cleanup                           Tüm Docker objelerini temizle"
    echo "  --backup                            Durdurma öncesi backup al"
    echo "  --help, -h                          Bu yardım mesajını göster"
    echo ""
    echo "Örnekler:"
    echo "  $0                                  # Varsayılan graceful shutdown"
    echo "  $0 --graceful --backup              # Backup alarak güvenli durdur"
    echo "  $0 --force                          # Hemen durdur"
    echo "  $0 --cleanup                        # Tüm Docker objelerini temizle"
}

# Varsayılan değerler
GRACEFUL_SHUTDOWN=true
FORCE_SHUTDOWN=false
REMOVE_VOLUMES=false
REMOVE_IMAGES=false
FULL_CLEANUP=false
CREATE_BACKUP=false

# Parametreleri parse et
while [[ $# -gt 0 ]]; do
    case $1 in
        --graceful)
            GRACEFUL_SHUTDOWN=true
            FORCE_SHUTDOWN=false
            shift
            ;;
        --force)
            FORCE_SHUTDOWN=true
            GRACEFUL_SHUTDOWN=false
            shift
            ;;
        --remove-volumes)
            REMOVE_VOLUMES=true
            shift
            ;;
        --remove-images)
            REMOVE_IMAGES=true
            shift
            ;;
        --cleanup)
            FULL_CLEANUP=true
            shift
            ;;
        --backup)
            CREATE_BACKUP=true
            shift
            ;;
        --help|-h)
            usage
            exit 0
            ;;
        *)
            error "Bilinmeyen parametre: $1"
            usage
            exit 1
            ;;
    esac
done

# Docker Compose komutunu belirle
if docker compose version &> /dev/null; then
    DOCKER_COMPOSE="docker compose"
else
    DOCKER_COMPOSE="docker-compose"
fi

log "Durdurma parametreleri:"
info "  Graceful Shutdown: $GRACEFUL_SHUTDOWN"
info "  Force Shutdown: $FORCE_SHUTDOWN"
info "  Remove Volumes: $REMOVE_VOLUMES"
info "  Remove Images: $REMOVE_IMAGES"
info "  Full Cleanup: $FULL_CLEANUP"
info "  Create Backup: $CREATE_BACKUP"

# Backup oluştur
if [ "$CREATE_BACKUP" = true ]; then
    log "Backup oluşturuluyor..."
    
    # Database backup
    if $DOCKER_COMPOSE ps mysql | grep -q "Up"; then
        log "Database backup'ı alınıyor..."
        BACKUP_DIR="./backups/$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$BACKUP_DIR"
        
        $DOCKER_COMPOSE exec mysql mysqldump -u root -p"${DB_PASSWORD:-password}" "${DB_DATABASE:-modularecommerce}" > "$BACKUP_DIR/database.sql" 2>/dev/null || warn "Database backup alınamadı"
        
        # Redis backup
        if $DOCKER_COMPOSE ps redis | grep -q "Up"; then
            log "Redis backup'ı alınıyor..."
            $DOCKER_COMPOSE exec redis redis-cli BGSAVE || warn "Redis backup alınamadı"
            sleep 2
            docker cp modularecommerce_redis:/data/dump.rdb "$BACKUP_DIR/redis.rdb" 2>/dev/null || warn "Redis dump dosyası kopyalanamadı"
        fi
        
        # Storage backup
        log "Storage dosyaları backup'ı alınıyor..."
        tar -czf "$BACKUP_DIR/storage.tar.gz" storage/ 2>/dev/null || warn "Storage backup alınamadı"
        
        log "Backup tamamlandı: $BACKUP_DIR"
    else
        warn "MySQL container çalışmıyor, backup alınamıyor"
    fi
fi

# Graceful shutdown
if [ "$GRACEFUL_SHUTDOWN" = true ] && [ "$FORCE_SHUTDOWN" = false ]; then
    log "Graceful shutdown başlatılıyor..."
    
    # Queue worker'ları güvenli şekilde durdur
    log "Queue worker'lar durduruluyor..."
    
    # Horizon'u durdur
    if $DOCKER_COMPOSE ps | grep -q "modularecommerce_app.*Up\|modularecommerce_horizon.*Up"; then
        log "Horizon durduruluyor..."
        $DOCKER_COMPOSE exec app php artisan horizon:terminate 2>/dev/null || \
        $DOCKER_COMPOSE exec horizon php artisan horizon:terminate 2>/dev/null || \
        warn "Horizon durdurulamadı veya çalışmıyor"
        
        # Horizon'un durmasını bekle
        log "Horizon'un durması bekleniyor..."
        sleep 10
    fi
    
    # Çalışan job'ları kontrol et
    log "Çalışan job'lar kontrol ediliyor..."
    if $DOCKER_COMPOSE ps | grep -q "modularecommerce_app.*Up"; then
        RUNNING_JOBS=$($DOCKER_COMPOSE exec app php artisan queue:monitor 2>/dev/null | grep -c "processing" || echo "0")
        if [ "$RUNNING_JOBS" -gt 0 ]; then
            warn "$RUNNING_JOBS adet job hala çalışıyor. Bitmesi bekleniyor..."
            
            # Maksimum 60 saniye bekle
            for i in {1..12}; do
                sleep 5
                RUNNING_JOBS=$($DOCKER_COMPOSE exec app php artisan queue:monitor 2>/dev/null | grep -c "processing" || echo "0")
                if [ "$RUNNING_JOBS" -eq 0 ]; then
                    log "Tüm job'lar tamamlandı."
                    break
                fi
                info "Hala $RUNNING_JOBS job çalışıyor... ($((i*5))/60 saniye)"
            done
            
            if [ "$RUNNING_JOBS" -gt 0 ]; then
                warn "Bazı job'lar hala çalışıyor, zorla durduruluyor..."
            fi
        fi
    fi
    
    # Supervisor'a graceful shutdown sinyali gönder
    log "Supervisor'a shutdown sinyali gönderiliyor..."
    $DOCKER_COMPOSE exec app supervisorctl shutdown 2>/dev/null || \
    $DOCKER_COMPOSE exec queue-worker supervisorctl shutdown 2>/dev/null || \
    $DOCKER_COMPOSE exec horizon supervisorctl shutdown 2>/dev/null || \
    warn "Supervisor shutdown sinyali gönderilemedi"
    
    # Container'ların durmasını bekle
    log "Container'ların durması bekleniyor..."
    sleep 5
fi

# Container'ları durdur
log "Container'lar durduruluyor..."

if [ "$FORCE_SHUTDOWN" = true ]; then
    warn "Force shutdown - Container'lar hemen durduruluyor!"
    $DOCKER_COMPOSE kill
else
    $DOCKER_COMPOSE stop
fi

# Container'ları kaldır
log "Container'lar kaldırılıyor..."
if [ "$REMOVE_VOLUMES" = true ]; then
    warn "Volume'lar da silinecek!"
    $DOCKER_COMPOSE down -v --remove-orphans
else
    $DOCKER_COMPOSE down --remove-orphans
fi

# Image'ları kaldır
if [ "$REMOVE_IMAGES" = true ]; then
    log "Docker image'ları siliniyor..."
    
    # Proje image'larını bul ve sil
    PROJECT_IMAGES=$(docker images | grep modularecommerce | awk '{print $3}' | sort -u)
    if [ ! -z "$PROJECT_IMAGES" ]; then
        echo "$PROJECT_IMAGES" | xargs docker rmi -f 2>/dev/null || warn "Bazı image'lar silinemedi"
    fi
    
    # Dangling image'ları temizle
    docker image prune -f
fi

# Full cleanup
if [ "$FULL_CLEANUP" = true ]; then
    warn "Full cleanup - Tüm Docker objeleri temizleniyor!"
    
    # Tüm container'ları durdur ve sil
    log "Tüm container'lar temizleniyor..."
    docker container prune -f
    
    # Tüm image'ları sil
    log "Tüm image'lar temizleniyor..."
    docker image prune -a -f
    
    # Tüm volume'ları sil
    log "Tüm volume'lar temizleniyor..."
    docker volume prune -f
    
    # Tüm network'leri sil
    log "Tüm network'ler temizleniyor..."
    docker network prune -f
    
    # Build cache'i temizle
    log "Build cache temizleniyor..."
    docker builder prune -a -f
    
    # System prune
    log "Sistem temizleniyor..."
    docker system prune -a -f --volumes
fi

# Son kontroller
log "Son kontroller yapılıyor..."

# Container'ların durduğunu kontrol et
RUNNING_CONTAINERS=$(docker ps | grep modularecommerce | wc -l)
if [ "$RUNNING_CONTAINERS" -eq 0 ]; then
    log "✅ Tüm container'lar başarıyla durduruldu."
else
    warn "⚠️ $RUNNING_CONTAINERS container hala çalışıyor:"
    docker ps | grep modularecommerce
fi

# Volume durumu
if [ "$REMOVE_VOLUMES" = false ]; then
    VOLUMES=$(docker volume ls | grep modularecommerce | wc -l)
    if [ "$VOLUMES" -gt 0 ]; then
        info "📦 $VOLUMES volume korundu:"
        docker volume ls | grep modularecommerce
    fi
fi

# Disk alanı bilgisi
log "Disk alanı durumu:"
df -h . | tail -1

# Özet
echo ""
log "🏁 Laravel Queue Worker Docker durdurma işlemi tamamlandı!"
echo ""

if [ "$CREATE_BACKUP" = true ] && [ -d "./backups" ]; then
    info "💾 Backup'lar: ./backups/ dizininde"
fi

if [ "$REMOVE_VOLUMES" = false ]; then
    info "💡 Yeniden başlatmak için: ./docker-queue-start.sh"
else
    info "💡 Fresh start için: ./docker-queue-start.sh --fresh"
fi

if [ "$FULL_CLEANUP" = true ]; then
    info "🧹 Tam temizlik yapıldı. Yeniden başlatma daha uzun sürebilir."
fi

echo ""
info "=== Yararlı Komutlar ==="
info "Container durumu: docker ps -a"
info "Volume'lar: docker volume ls"
info "Image'lar: docker images"
info "Disk kullanımı: docker system df"

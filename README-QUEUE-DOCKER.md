# Laravel Queue Worker Docker Kı<PERSON><PERSON>, Dock<PERSON> ba<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>nda Laravel queue worker'larını otomatik olarak başlatacak gelişmiş yapılandırmayı açıklar.

## 🚀 Özellikler

- **Otomatik Queue Worker Başlatma**: Container başlatıldığında queue worker'lar otomatik olarak çalışır
- **Çoklu Strateji Desteği**: Horizon, geleneksel worker'lar veya her ikisi birden
- **Akıllı Health Check**: Container ve worker sağlığını otomatik kontrol eder
- **Graceful Shutdown**: Container kapatılırken worker'ları gü<PERSON>li şekilde durdurur
- **Gelişmiş Monitoring**: Canlı izleme ve log takibi
- **Esnek Yapılandırma**: Environment değişkenleri ile kolay özelleştirme

## 📁 Do<PERSON><PERSON> Yapısı

```
├── docker/
│   ├── entrypoint.sh              # Container ba<PERSON><PERSON><PERSON> script'i
│   ├── queue-manager.sh           # Queue yönetim aracı
│   └── supervisor/
│       └── supervisord.conf       # Supervisor yapılandırması
├── docker-compose.yml             # Docker Compose yapılandırması
├── docker-queue-start.sh          # Başlatma script'i
├── docker-queue-stop.sh           # Durdurma script'i
├── .env.docker.example            # Environment değişkenleri örneği
└── README-QUEUE-DOCKER.md         # Bu dosya
```

## 🛠️ Kurulum

### 1. Environment Dosyasını Hazırlayın

```bash
cp .env.docker.example .env
```

`.env` dosyasını düzenleyip gerekli ayarları yapın:

```env
# Queue Worker Stratejisi
QUEUE_STRATEGY=horizon          # horizon, workers, both

# Database Ayarları
DB_DATABASE=modularecommerce
DB_USERNAME=root
DB_PASSWORD=password

# Redis Ayarları
REDIS_HOST=redis
REDIS_PASSWORD=null

# Otomatik Migration
AUTO_MIGRATE=false
```

### 2. Docker Container'larını Başlatın

#### Basit Başlatma
```bash
./docker-queue-start.sh
```

#### Gelişmiş Seçeneklerle
```bash
# Horizon stratejisi ile build ederek
./docker-queue-start.sh --strategy horizon --build

# Sadece worker'lar ile migration'la
./docker-queue-start.sh --strategy workers --migrate

# Fresh start ile seed'lerle
./docker-queue-start.sh --fresh --seed --monitor
```

## 🎯 Queue Worker Stratejileri

### 1. Horizon Stratejisi (Önerilen)
```bash
QUEUE_STRATEGY=horizon
```
- Laravel Horizon kullanır
- Web tabanlı dashboard
- Otomatik load balancing
- Gelişmiş monitoring

### 2. Worker Stratejisi
```bash
QUEUE_STRATEGY=workers
```
- Geleneksel `queue:work` komutları
- Her queue için ayrı worker process'leri
- Daha fazla kontrol imkanı

### 3. Hibrit Strateji (Dikkatli Kullanın)
```bash
QUEUE_STRATEGY=both
```
- Hem Horizon hem worker'lar aktif
- Test ortamları için uygun

## 📊 Queue Yönetimi

### Container İçinde Queue Yönetimi

```bash
# Container'a bağlan
docker exec -it modularecommerce_app bash

# Queue durumunu kontrol et
queue-manager status

# Belirli bir worker'ı yeniden başlat
queue-manager restart queue-emails

# Canlı monitoring
queue-manager monitor

# Log'ları takip et
queue-manager logs queue-orders

# Sistem sağlığını kontrol et
queue-manager health
```

### Dışarıdan Queue Yönetimi

```bash
# Queue durumu
docker exec modularecommerce_app queue-manager status

# Horizon'u yeniden başlat
docker exec modularecommerce_app queue-manager restart horizon

# Strateji değiştir
docker exec modularecommerce_app queue-manager switch workers
```

## 🔧 Yapılandırma

### Supervisor Ayarları

`docker/supervisor/supervisord.conf` dosyasında worker'lar tanımlanmıştır:

```ini
# Horizon
[program:horizon]
command=php /var/www/artisan horizon
autostart=true
autorestart=true

# Email Queue Worker
[program:queue-emails]
command=php /var/www/artisan queue:work redis --queue=emails
numprocs=2
autostart=false
```

### Environment Değişkenleri

| Değişken | Varsayılan | Açıklama |
|----------|------------|----------|
| `QUEUE_STRATEGY` | `horizon` | Queue worker stratejisi |
| `AUTO_MIGRATE` | `false` | Otomatik migration çalıştır |
| `CLEAR_FAILED_JOBS` | `false` | Başarısız job'ları temizle |
| `QUEUE_DEFAULT_PROCESSES` | `2` | Default queue process sayısı |
| `QUEUE_EMAIL_PROCESSES` | `2` | Email queue process sayısı |
| `QUEUE_ORDER_PROCESSES` | `3` | Order queue process sayısı |

## 📈 Monitoring ve Loglar

### Horizon Dashboard
```
http://localhost/horizon
```

### Log Dosyaları
```bash
# Tüm queue log'ları
docker exec modularecommerce_app tail -f /var/www/storage/logs/queue-*.log

# Horizon log'ları
docker exec modularecommerce_app tail -f /var/www/storage/logs/horizon.log

# Supervisor log'ları
docker exec modularecommerce_app tail -f /var/log/supervisor/supervisord.log
```

### Canlı Monitoring
```bash
# Container içinde
queue-manager monitor

# Dışarıdan
docker exec -it modularecommerce_app queue-manager monitor
```

## 🛑 Durdurma

### Graceful Shutdown (Önerilen)
```bash
./docker-queue-stop.sh --graceful
```

### Backup ile Durdurma
```bash
./docker-queue-stop.sh --graceful --backup
```

### Force Shutdown
```bash
./docker-queue-stop.sh --force
```

### Tam Temizlik
```bash
./docker-queue-stop.sh --cleanup
```

## 🔍 Sorun Giderme

### Container Başlamıyor
```bash
# Log'ları kontrol et
docker-compose logs app

# Health check
docker exec modularecommerce_app queue-manager health
```

### Queue Worker'lar Çalışmıyor
```bash
# Worker durumunu kontrol et
docker exec modularecommerce_app queue-manager status

# Supervisor'ı yeniden başlat
docker exec modularecommerce_app supervisorctl restart all
```

### Database Bağlantı Problemi
```bash
# Database container'ını kontrol et
docker-compose ps mysql

# Bağlantıyı test et
docker exec modularecommerce_app php artisan migrate:status
```

### Redis Bağlantı Problemi
```bash
# Redis container'ını kontrol et
docker-compose ps redis

# Redis bağlantısını test et
docker exec modularecommerce_app php artisan tinker --execute="Redis::ping();"
```

## 🎛️ Docker Compose Profilleri

### Varsayılan Profil
```bash
docker-compose up -d
```
Tüm servisler: app, nginx, mysql, redis

### Sadece Worker'lar
```bash
docker-compose --profile workers-only up -d
```
Sadece: queue-worker, mysql, redis

### Sadece Horizon
```bash
docker-compose --profile horizon-only up -d
```
Sadece: horizon, mysql, redis

## 📋 Komut Referansı

### Başlatma Script'i
```bash
./docker-queue-start.sh [SEÇENEKLER]

Seçenekler:
  --strategy [horizon|workers|both]  Queue worker stratejisi
  --profile [default|workers-only|horizon-only]  Docker profili
  --build                             Image'ları yeniden build et
  --fresh                             Fresh start
  --migrate                           Otomatik migration
  --seed                              Database seed'leri
  --logs                              Başlatma sonrası logları göster
  --monitor                           Monitoring başlat
```

### Durdurma Script'i
```bash
./docker-queue-stop.sh [SEÇENEKLER]

Seçenekler:
  --graceful                          Güvenli durdurma
  --force                             Zorla durdurma
  --remove-volumes                    Volume'ları sil
  --remove-images                     Image'ları sil
  --cleanup                           Tam temizlik
  --backup                            Backup al
```

### Queue Manager
```bash
queue-manager [KOMUT] [SEÇENEKLER]

Komutlar:
  status              Durumu göster
  start [worker]      Worker'ı başlat
  stop [worker]       Worker'ı durdur
  restart [worker]    Worker'ı yeniden başlat
  logs [worker]       Log'ları göster
  monitor             Canlı monitoring
  health              Sağlık kontrolü
  switch [strategy]   Strateji değiştir
```

## 🚨 Önemli Notlar

1. **Production Kullanımı**: Production ortamında `AUTO_MIGRATE=true` yapmadan önce dikkatli olun
2. **Resource Limits**: Container'lar için uygun CPU ve memory limitleri ayarlayın
3. **Backup**: Önemli veriler için düzenli backup alın
4. **Monitoring**: Production'da external monitoring araçları kullanın
5. **Security**: Redis ve MySQL için güçlü şifreler kullanın

## 📞 Destek

Sorun yaşadığınızda:

1. Log'ları kontrol edin: `docker-compose logs`
2. Health check yapın: `queue-manager health`
3. Container durumunu kontrol edin: `docker ps -a`
4. Disk alanını kontrol edin: `docker system df`

Bu kılavuz ile Laravel queue worker'larınızı Docker ortamında sorunsuz çalıştırabilirsiniz! 🎉

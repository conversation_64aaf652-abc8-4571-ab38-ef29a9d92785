# Laravel Queue Worker Docker Environment Variables
# Bu dosyayı .env olarak kopyalayın ve değerlerinizi ayarlayın

# Laravel Temel Ayarları
APP_NAME="Modular E-commerce"
APP_ENV=local
APP_KEY=base64:your-app-key-here
APP_DEBUG=true
APP_URL=http://localhost

# Database Ayarları
DB_CONNECTION=mysql
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=modularecommerce
DB_USERNAME=root
DB_PASSWORD=password

# Redis Ayarları
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

# Queue Ayarları
QUEUE_CONNECTION=redis
BROADCAST_DRIVER=redis
CACHE_DRIVER=redis
SESSION_DRIVER=redis

# Queue Worker Stratejisi
# Seçenekler: horizon, workers, both
QUEUE_STRATEGY=horizon

# Otomatik Migration (Production için true yapabilirsiniz)
AUTO_MIGRATE=false

# Başarısız job'ları temizle (Container ba<PERSON><PERSON><PERSON>ldığında)
CLEAR_FAILED_JOBS=false

# Mail Ayarları
MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# Admin Email (Horizon bildirimleri için)
ADMIN_EMAIL=<EMAIL>

# Logging
LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Horizon Ayarları
HORIZON_DOMAIN=localhost
HORIZON_PATH=horizon

# Queue Monitoring
QUEUE_MONITOR_ENABLED=true
QUEUE_FAILED_DRIVER=database-uuids

# Performance Ayarları
QUEUE_RETRY_AFTER=90
QUEUE_MAX_TRIES=3
QUEUE_TIMEOUT=60

# Specific Queue Timeouts
QUEUE_EMAIL_TIMEOUT=120
QUEUE_ORDER_TIMEOUT=300
QUEUE_REALTIME_TIMEOUT=30
QUEUE_CRITICAL_TIMEOUT=15
QUEUE_INVENTORY_TIMEOUT=180
QUEUE_SHIPPING_TIMEOUT=240

# Worker Process Counts (Production için artırabilirsiniz)
QUEUE_DEFAULT_PROCESSES=2
QUEUE_EMAIL_PROCESSES=2
QUEUE_ORDER_PROCESSES=3
QUEUE_REALTIME_PROCESSES=2
QUEUE_CRITICAL_PROCESSES=2
QUEUE_INVENTORY_PROCESSES=2
QUEUE_SHIPPING_PROCESSES=2

# Memory Limits (MB)
QUEUE_MEMORY_LIMIT=128
QUEUE_MAX_TIME=3600

# Supervisor Ayarları
SUPERVISOR_LOG_LEVEL=info
SUPERVISOR_MINFDS=1024
SUPERVISOR_MINPROCS=200

# Health Check Ayarları
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10
HEALTH_CHECK_RETRIES=3

# Development/Debug Ayarları
TELESCOPE_ENABLED=false
DEBUGBAR_ENABLED=false
QUERY_LOG_ENABLED=false

# File Permissions
FILE_PERMISSION_MODE=755
STORAGE_PERMISSION_MODE=755

# Container Restart Policy
RESTART_POLICY=unless-stopped

# Network Settings
NETWORK_NAME=modularecommerce

# Volume Settings
MYSQL_VOLUME=mysql_data
REDIS_VOLUME=redis_data

# Port Mappings
HTTP_PORT=80
HTTPS_PORT=443
MYSQL_PORT=3306
REDIS_PORT=6379

# SSL/TLS (Production için)
SSL_ENABLED=false
SSL_CERT_PATH=
SSL_KEY_PATH=

# Backup Ayarları
BACKUP_ENABLED=false
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS=7

# Monitoring & Alerting
MONITORING_ENABLED=false
ALERT_EMAIL=
SLACK_WEBHOOK_URL=

# Security
SECURITY_HEADERS_ENABLED=true
RATE_LIMITING_ENABLED=true
CSRF_PROTECTION_ENABLED=true

# API Ayarları
API_RATE_LIMIT=60
API_THROTTLE_ENABLED=true

# Session Ayarları
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_HTTP_ONLY=true
SESSION_SAME_SITE=lax

# CORS Ayarları
CORS_ALLOWED_ORIGINS="*"
CORS_ALLOWED_METHODS="*"
CORS_ALLOWED_HEADERS="*"

# Timezone
APP_TIMEZONE=Europe/Istanbul

# Locale
APP_LOCALE=tr
APP_FALLBACK_LOCALE=en

# Broadcasting
PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

# AWS (S3, SES, etc.)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

# Elasticsearch (Opsiyonel)
ELASTICSEARCH_HOST=elasticsearch
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_SCHEME=http
ELASTICSEARCH_USER=
ELASTICSEARCH_PASS=

# Memcached (Opsiyonel)
MEMCACHED_HOST=memcached
MEMCACHED_PORT=11211

# Additional Services
MAILHOG_ENABLED=false
PHPMYADMIN_ENABLED=false
REDIS_COMMANDER_ENABLED=false
